---
apiVersion: v1
kind: Namespace
metadata:
  name: ${K8S_NAMESPACE} 
--- 
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${CI_PROJECT_NAME}
  namespace: ${K8S_NAMESPACE}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ${CI_PROJECT_NAME}
  template:
    metadata:
      labels:
        app: ${CI_PROJECT_NAME}
    spec:
      imagePullSecrets:
        - name: nexuscred
      containers:
        - name: ${CI_PROJECT_NAME}-container
          image: ${IMAGE_FULL_NAME}:${BUILD_ID}
          env:
            - name: BUILD_ID
              value: ${IMAGE_FULL_NAME}:${BUILD_ID}
            - name: K<PERSON><PERSON>POSTGEO_PREFIX
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: GEOSERVICE_KAZPOSTGEO_PREFIX
            - name: ENV_PREFIX
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: GEOSERVICE_ENV_PREFIX
            - name: DB_NAME
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: GEOSERVICE_DB_NAME
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: GEOSERVICE_DB_USER
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: GEOSERVICE_DB_PASSWORD
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: GEOSERVICE_DB_HOST
            - name: DB_PORT
              value: "5432"
          
          ports:
            - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: ${CI_PROJECT_NAME}-service
  namespace: ${K8S_NAMESPACE}
spec:
  selector:
    app: ${CI_PROJECT_NAME}
  ports:
    - protocol: TCP
      port: 443
      targetPort: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ${CI_PROJECT_NAME}-ingress
  namespace: ${K8S_NAMESPACE}
  annotations:
    #kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /npi-dev/geoservice/$2
spec:
  ingressClassName: nginx
  tls:
    - hosts:
      - services.post.kz
      secretName: services.post.kz-tls
  rules:
    - host: services.post.kz
      http:
        paths:
          - path: /npi-dev/geoservice(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ${CI_PROJECT_NAME}-service
                port:
                  number: 443
          #- path: /npi-dev/geoservice/admin
          #  pathType: Prefix
          #  backend:
          #    service:
          #      name: ${CI_PROJECT_NAME}-service
          #      port:
          #        number: 443
          #- path: /npi-dev/geoservice/static
          #  pathType: Prefix
          #  backend:
          #    service:
          #      name: ${CI_PROJECT_NAME}-service
          #      port:
          #        number: 443
          #- path: /npi-dev/geoservice/layer_map
          #  pathType: Prefix
          #  backend:
          #    service:
          #      name: ${CI_PROJECT_NAME}-service
          #      port:
          #        number: 443
          #- path: /npi-dev/geoservice/get_layer_polygons
          #  pathType: Prefix
          #  backend:
          #    service:
          #      name: ${CI_PROJECT_NAME}-service
          #      port:
          #        number: 443
          #- path: /npi-dev/geoservice/get_departments
          #  pathType: Prefix
          #  backend:
          #    service:
          #      name: ${CI_PROJECT_NAME}-service
          #      port:
          #        number: 443
          #- path: /npi-dev/geoservice/save_polygon
          #  pathType: Prefix
          #  backend:
          #    service:
          #      name: ${CI_PROJECT_NAME}-service
          #      port:
          #        number: 443
          #- path: /npi-dev/geoservice/update_department
          #  pathType: Prefix
          #  backend:
          #    service:
          #      name: ${CI_PROJECT_NAME}-service
          #      port:
          #        number: 443
          #- path: /npi-dev/geoservice/update_department/
          #  pathType: Prefix
          #  backend:
          #    service:
          #      name: ${CI_PROJECT_NAME}-service
          #      port:
          #        number: 443
#
          #- path: /npi-dev/geoservice/update-department
          #  pathType: Prefix
          #  backend:
          #    service:
          #      name: ${CI_PROJECT_NAME}-service
          #      port:
          #        number: 443
          #- path: /npi-dev/geoservice/update-department/
          #  pathType: Prefix
          #  backend:
          #    service:
          #      name: ${CI_PROJECT_NAME}-service
          #      port:
          #        number: 443

