from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.models import User, Group
from django.urls import path
from django.shortcuts import render
from django.http import HttpResponseRedirect
from mapping.models import Layers, Hubs, Departments, Auto, Region, DepartmentType, ProductCategoryLayers, \
    UserRegionAccess
from .admin_forms import HubForm
from dal import autocomplete
from django.conf import settings

admin.site.site_header = 'Kazpost Digital'
admin.site.site_title = 'Панель администратора'
admin.site.index_title = "Панель администратора"
#admin.site.unregister(User)
#admin.site.unregister(Group)
#admin.site.site_url = "npi/geoservice/layer_map"
admin.site.site_url = '/'+settings.KAZPOSTGEO_PREFIX+'/layer_map'


# Кастомная админ-страница для редактирования хабов
class HubsEditorAdminView:
    """Админ-страница для группового редактирования хабов"""

    class Media:
        css = {
            'all': (
                'map/css/leaflet.css',
                'map/css/leaflet.draw.css',
                'map/css/Control.Geocoder.css',
            )
        }
        js = (
            'map/js/leaflet.js',
            'map/js/leaflet.draw.js',
            'map/js/Control.Geocoder.js',
            'map/js/hubs_editor.js',
        )

def hubs_editor_admin_view(request):
    """Админ-страница для группового редактирования хабов"""
    if not request.user.is_staff:
        return HttpResponseRedirect('/admin/login/')

    # Получаем данные как в обычной view
    layers = Layers.objects.all()
    regions = Region.objects.all()

    # Фильтрация регионов по доступам пользователя
    user_region_access_list = UserRegionAccess.objects.filter(user=request.user)
    region_with_access = []
    for user_access in user_region_access_list:
        if user_access.has_full_access:
            region_with_access.extend(Region.objects.all())
            break
        else:
            region_with_access.append(user_access.region)

    context = {
        'title': 'Групповое редактирование полигонов',
        'layers': layers,
        'regions': region_with_access,
        'has_permission': True,
        'site_header': admin.site.site_header,
        'site_title': admin.site.site_title,
        'index_title': admin.site.index_title,
        'available_apps': admin.site.get_app_list(request),
        'is_nav_sidebar_enabled': True,
        # Добавляем Media
        'media': HubsEditorAdminView.Media(),
        # Добавляем префикс для URL
        'KAZPOSTGEO_PREFIX': settings.KAZPOSTGEO_PREFIX,
    }

    return render(request, 'admin/mapping/hubs_editor_admin.html', context)


# Кастомный админ для модели-заглушки
class HubsMapEditorAdmin(admin.ModelAdmin):
    """Админ для редиректа на редактор карты"""

    def changelist_view(self, request, extra_context=None):
        # Перенаправляем на наш редактор вместо списка
        return HttpResponseRedirect('/admin/hubs-editor/')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return request.user.is_staff

    def has_delete_permission(self, request, obj=None):
        return False

# Регистрируем модель-заглушку
from .models import HubsMapEditor
admin.site.register(HubsMapEditor, HubsMapEditorAdmin)

   
#@admin.register(Departments)
#class DepartmentAdmin(admin.ModelAdmin):
#    list_display = ('name', 'code', 'post_code', 'new_post_code','lat', 'lon')


@admin.register(Hubs)
class HubAdmin(admin.ModelAdmin):
    form = HubForm
    list_display = ('department', 'layers', 'is_active')
    autocomplete_fields = ('department',)
    search_fields = ["department__name", ]

    class Media:
        js = ('map/js/hub.js',)

    def get_queryset(self, request):
        return Hubs.objects.filter(id__gte=0)

@admin.register(AllHubsFake)
class AllHubAdmin(admin.ModelAdmin):
    form = AllHubsForm



class DepartmentsAutocomplete(autocomplete.Select2QuerySetView):
    def get_queryset(self):
        qs = Departments.objects.all()
        if self.q:
            qs = qs.filter(name__icontains=self.q)
        return qs

    def get_placeholder(self):
        return "Поиск"


admin.register(Departments, DepartmentsAutocomplete)
#@admin.register(Region)

@admin.register(Departments)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'post_code', 'region', 'status', 'type', 'class_field','location_name','dm_tindex', 'sub_department')
    #list_display = ('name', 'post_code',  'status')
    list_per_page = 100
    search_fields = ['post_code', 'name','dm_tindex']
    ordering = ('name',)
    list_filter = ["region", ]


@admin.register(Layers)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('code', 'name')

@admin.register(ProductCategoryLayers)
class ProductCategoryLayersAdmin(admin.ModelAdmin):
    list_display = ('product_code', 'product_name', 'layer')
    list_filter = ["layer_id", ]
    search_fields = ('product_code', 'product_name')
    ordering = ('product_code',)

@admin.register(Auto)
class AutoAdmin(admin.ModelAdmin):
    list_display = ('name_tech_object', 'equipment_num', 'kind', 'status' )
    search_fields = ["name_tech_object", "equipment_num"]


@admin.register(DepartmentType)
class DepartmentTypeAdmin(admin.ModelAdmin):
    list_display = ('code', 'id' )


class UserRegionAccessInline(admin.TabularInline):
    model = UserRegionAccess
    extra = 1

class CustomUserAdmin(UserAdmin):
    inlines = (UserRegionAccessInline,)

admin.site.unregister(User)
admin.site.register(User, CustomUserAdmin)
