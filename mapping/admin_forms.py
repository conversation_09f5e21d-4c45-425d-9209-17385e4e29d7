from django.contrib.auth.models import User
from django.contrib.gis import forms
from django.forms.widgets import Widget
from django.template.loader import render_to_string
from django.utils.safestring import mark_safe
from leaflet.forms.widgets import LeafletWidget

from mapping.models import AllHubsFake


class HubForm(forms.ModelForm):
    polygon = forms.PolygonField(
        widget=forms.OSMWidget(
            attrs={
                'map_width': 800, 'map_height': 500,
                'default_lat': 43.257109, 'default_lon': 76.946314
            }
        ), label='Полигон', required=False
    )


class AllHubsForm(forms.ModelForm):
    class Meta:
        model = AllHubsFake
        fields = ['polygon']
        widgets = {
            'polygon': LeafletWidget(attrs={
                'map_width': 800,
                'map_height': 500,
                'geom_type': 'Polygon',
                'default_zoom': 6,
                'default_lon': 37.618423,
                'default_lat': 55.751244,
            }),
        }
